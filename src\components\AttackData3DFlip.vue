<template>
  <div class="attack-data-display">
    <!-- 上半部分区域 -->
    <div class="data-section top-section">
      <div class="attack-panel">
        <div class="panel-content">
          <div class="label-container" :class="{ sliding: topSliding }" :style="{ '--animation-duration': animationDuration + 'ms' }">
            <Label2 v-if="dataStore.attackAlarmData.length > 0" :attack-info="dataStore.attackAlarmData[topIndex]" @confirmed="handleTopConfirmed" />
            <div v-else class="no-data">暂无数据</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分区域 -->
    <div class="data-section bottom-section">
      <div class="attack-panel">
        <div class="panel-content">
          <div class="label-container" :class="{ sliding: bottomSliding }" :style="{ '--animation-duration': animationDuration + 'ms' }">
            <Label2 v-if="dataStore.attackAlarmData.length > 0" :attack-info="dataStore.attackAlarmData[bottomIndex]" @confirmed="handleBottomConfirmed" />
            <div v-else class="no-data">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from "vue";
import { usedata } from "../store/data";
import Label2 from "../views/Label2.vue";

const dataStore = usedata();

const topIndex = ref(0);
const bottomIndex = ref(0);
const topSliding = ref(false);
const bottomSliding = ref(false);

// 动画速度控制变量（毫秒）
const animationDuration = ref(600); // 滑动动画持续时间
const autoSwitchInterval = ref(5000); // 自动切换间隔时间（毫秒）
const enableAutoSwitch = ref(true); // 是否启用自动切换

// 定时器引用
let topTimer = null;
let bottomTimer = null;

// 获取下一个有效索引，确保上下面板显示不同数据
const getNextValidIndex = (currentIndex, isTop) => {
  const dataLength = dataStore.attackAlarmData.length;
  if (dataLength <= 1) return 0;

  const otherIndex = isTop ? bottomIndex.value : topIndex.value;
  let nextIndex = (currentIndex + 1) % dataLength;

  // 如果下一个索引和另一个面板的索引相同，再跳一个
  if (nextIndex === otherIndex && dataLength > 2) {
    nextIndex = (nextIndex + 1) % dataLength;
  }

  return nextIndex;
};

// 滑动动画函数
const performSlideAnimation = async (isTop = true) => {
  const slidingRef = isTop ? topSliding : bottomSliding;
  const indexRef = isTop ? topIndex : bottomIndex;

  console.log(`🚀 开始执行${isTop ? "上" : "下"}面板动画，当前索引:`, indexRef.value, "数据长度:", dataStore.attackAlarmData.length);

  // 如果没有数据，重置索引
  if (dataStore.attackAlarmData.length === 0) {
    indexRef.value = 0;
    return;
  }

  // 如果只有一条数据，不执行动画，直接重置索引
  if (dataStore.attackAlarmData.length === 1) {
    indexRef.value = 0;
    return;
  }

  // 开始滑动动画
  slidingRef.value = true;

  // 在动画50%时更新索引
  setTimeout(() => {
    if (dataStore.attackAlarmData.length > 0) {
      // 获取下一个有效索引，确保不与另一个面板重复
      const nextIndex = getNextValidIndex(indexRef.value, isTop);
      indexRef.value = nextIndex;
      console.log(`🚀 ${isTop ? "上" : "下"}面板索引更新为:`, nextIndex);
    } else {
      indexRef.value = 0;
    }
  }, animationDuration.value / 2);

  // 动画结束后重置状态
  setTimeout(() => {
    slidingRef.value = false;
    console.log(`🚀 ${isTop ? "上" : "下"}面板动画结束`);
  }, animationDuration.value);
};

// 处理上半部分确认事件
const handleTopConfirmed = async (confirmedId) => {
  console.log("🚀 上半部分确认事件，ID:", confirmedId);
  // 触发上半部分的滑动动画切换到下一条数据
  await performSlideAnimation(true);
};

// 处理下半部分确认事件
const handleBottomConfirmed = async (confirmedId) => {
  console.log("🚀 下半部分确认事件，ID:", confirmedId);
  // 触发下半部分的滑动动画切换到下一条数据
  await performSlideAnimation(false);
};

// 启动自动切换定时器
const startAutoSwitch = () => {
  if (!enableAutoSwitch.value) return;

  // 清除现有定时器
  stopAutoSwitch();

  // 为上下面板设置不同的定时器，避免同时切换
  topTimer = setInterval(async () => {
    if (dataStore.attackAlarmData.length > 1) {
      await performSlideAnimation(true);
    }
  }, autoSwitchInterval.value);

  // 下面板延迟一半时间开始，错开切换时机
  setTimeout(() => {
    bottomTimer = setInterval(async () => {
      if (dataStore.attackAlarmData.length > 1) {
        await performSlideAnimation(false);
      }
    }, autoSwitchInterval.value);
  }, autoSwitchInterval.value / 2);
};

// 停止自动切换定时器
const stopAutoSwitch = () => {
  if (topTimer) {
    clearInterval(topTimer);
    topTimer = null;
  }
  if (bottomTimer) {
    clearInterval(bottomTimer);
    bottomTimer = null;
  }
};

// 组件挂载时启动自动切换
onMounted(() => {
  startAutoSwitch();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoSwitch();
});

// 注意：现在两个面板都直接使用 attackAlarmData 的完整数据
// topIndex 和 bottomIndex 分别控制各自面板显示的数据索引

// 监听数据变化，确保索引有效且上下面板显示不同数据
watch(
  () => dataStore.attackAlarmData.length,
  (newLength) => {
    console.log("🚀 数据长度变化:", newLength);

    // 当数据长度变化时，确保索引不超出范围且上下面板显示不同数据
    if (newLength === 0) {
      topIndex.value = 0;
      bottomIndex.value = 0;
    } else if (newLength === 1) {
      // 只有一条数据时，两个面板都显示同一条
      topIndex.value = 0;
      bottomIndex.value = 0;
    } else {
      // 多条数据时，确保上下面板显示不同数据
      if (topIndex.value >= newLength) {
        topIndex.value = 0;
      }

      // 下面板优先显示第二条数据，如果和上面板重复则调整
      if (bottomIndex.value >= newLength) {
        bottomIndex.value = topIndex.value === 0 ? 1 : 0;
      } else if (bottomIndex.value === topIndex.value && newLength > 1) {
        // 如果当前索引相同，调整下面板索引
        bottomIndex.value = (topIndex.value + 1) % newLength;
      }
    }

    console.log("🚀 索引调整后 - 上面板:", topIndex.value, "下面板:", bottomIndex.value);
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
.attack-data-display {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.data-section {
  width: 100%;
  height: 50%;
  padding: 2px 2px;
}

.attack-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
  font-family: AlimamaShuHeiTi, sans-serif;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}

.data-index {
  font-family: AlimamaShuHeiTi, sans-serif;
  font-size: 12px;
  color: #32fefc;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  position: relative; /* 为滑动动画提供定位基础 */
}

.label-container {
  width: 100%;
  height: 100%;
  position: relative;
  transition: all var(--animation-duration, 600ms) ease-out;
  will-change: transform, opacity;
}

/* 滑动动画效果 */
.label-container.sliding {
  animation: slideInOut var(--animation-duration, 600ms) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideInOut {
  0% {
    transform: translateX(0);
    opacity: 1;
  }

  45% {
    transform: translateX(100%);
    opacity: 0;
  }

  55% {
    transform: translateX(-100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.bottom-section {
  position: absolute;
  top: 213px;
  right: 49px;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-family: AlimamaShuHeiTi, sans-serif;
  font-size: 14px;
  color: #666;
}
</style>

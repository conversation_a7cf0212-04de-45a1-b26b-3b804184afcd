<template>
  <div class="attack-data-display">
    <!-- 上半部分区域 -->
    <div class="data-section top-section">
      <div class="attack-panel">
        <div class="panel-content">
          <div class="label-container" :class="{ sliding: topSliding }" :style="{ '--animation-duration': animationDuration + 'ms' }">
            <Label2 v-if="topData.length > 0" :attack-info="topData[topIndex]" @confirmed="handleTopConfirmed" />
            <div v-else class="no-data">暂无数据</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分区域 -->
    <div class="data-section bottom-section">
      <div class="attack-panel">
        <div class="panel-content">
          <div class="label-container" :class="{ sliding: bottomSliding }" :style="{ '--animation-duration': animationDuration + 'ms' }">
            <Label2 v-if="bottomData.length > 0" :attack-info="bottomData[bottomIndex]" @confirmed="handleBottomConfirmed" />
            <div v-else class="no-data">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { usedata } from "../store/data";
import Label2 from "../views/Label2.vue";

const dataStore = usedata();

const topIndex = ref(0);
const bottomIndex = ref(0);
const topSliding = ref(false);
const bottomSliding = ref(false);

// 动画速度控制变量（毫秒）
const animationDuration = ref(600); // 滑动动画持续时间

// 滑动动画函数
const performSlideAnimation = async (isTop = true) => {
  const slidingRef = isTop ? topSliding : bottomSliding;
  const indexRef = isTop ? topIndex : bottomIndex;
  const dataRef = isTop ? topData : bottomData;

  // 如果数据长度小于等于1，直接更新索引，不执行动画
  if (dataRef.value.length <= 1) {
    if (dataRef.value.length > 0) {
      indexRef.value = (indexRef.value + 1) % dataRef.value.length;
    } else {
      indexRef.value = 0;
    }
    return;
  }

  // 开始滑动动画
  slidingRef.value = true;

  // 在动画50%时更新数据
  setTimeout(() => {
    if (dataRef.value.length > 0) {
      indexRef.value = (indexRef.value + 1) % dataRef.value.length;
    } else {
      indexRef.value = 0;
    }
  }, animationDuration.value / 2);

  // 动画结束后重置状态
  setTimeout(() => {
    slidingRef.value = false;
  }, animationDuration.value);
};

// 处理上半部分确认事件
const handleTopConfirmed = async (confirmedId) => {
  console.log("🚀 上半部分确认事件，ID:", confirmedId);
  // 触发上半部分的滑动动画切换到下一条数据
  await performSlideAnimation(true);
};

// 处理下半部分确认事件
const handleBottomConfirmed = async (confirmedId) => {
  console.log("🚀 下半部分确认事件，ID:", confirmedId);
  // 触发下半部分的滑动动画切换到下一条数据
  await performSlideAnimation(false);
};

// 计算属性：根据索引将攻击数据分成两部分
const topData = computed(() => {
  if (!dataStore.attackAlarmData || dataStore.attackAlarmData.length === 0) {
    return [];
  }

  // 上半部分显示索引为偶数的数据 (0, 2, 4, ...)
  const filtered = dataStore.attackAlarmData.filter((_, index) => {
    return index % 2 === 0;
  });

  return filtered;
});

const bottomData = computed(() => {
  if (!dataStore.attackAlarmData || dataStore.attackAlarmData.length === 0) {
    return [];
  }

  // 下半部分显示索引为奇数的数据 (1, 3, 5, ...)
  const filtered = dataStore.attackAlarmData.filter((_, index) => {
    return index % 2 === 1;
  });

  return filtered;
});
</script>

<style lang="less" scoped>
.attack-data-display {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.data-section {
  width: 100%;
  height: 50%;
  padding: 2px 2px;
}

.attack-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
  font-family: AlimamaShuHeiTi, sans-serif;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}

.data-index {
  font-family: AlimamaShuHeiTi, sans-serif;
  font-size: 12px;
  color: #32fefc;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  position: relative; /* 为滑动动画提供定位基础 */
}

.label-container {
  width: 100%;
  height: 100%;
  position: relative;
  transition: all var(--animation-duration, 600ms) ease-out;
  will-change: transform, opacity;
}

/* 滑动动画效果 */
.label-container.sliding {
  animation: slideInOut var(--animation-duration, 600ms) cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideInOut {
  0% {
    transform: translateX(0);
    opacity: 1;
  }

  45% {
    transform: translateX(100%);
    opacity: 0;
  }

  55% {
    transform: translateX(-100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.bottom-section {
  position: absolute;
  top: 213px;
  right: 49px;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-family: AlimamaShuHeiTi, sans-serif;
  font-size: 14px;
  color: #666;
}
</style>

<template>
  <div class="Qx1">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { usedata } from "../store/data";
const dataStore = usedata();

// 定义props
const props = defineProps({
  enableLoadingAnimation: {
    type: Boolean,
    default: false,
  },
  animationDelay: {
    type: String,
    default: "0s",
  },
});

const chartRef = ref(null);
let myChart = null;
const isChartReady = ref(false);

const animationConfig = {
  // ==================== 全局动画配置 ====================
  speedMultiplier: 1.0, // 整体动画速度倍数 (0.5=慢一半, 1.0=正常, 2.0=快一倍)

  // ==================== 主动画配置 ====================
  baseDuration: 1500, // 基础动画持续时间(ms) - 折线图适合稍长的动画
  easing: "cubicOut", // 缓动函数 - cubicOut提供平滑的减速效果
  baseDelayInterval: 100, // 基础系列间延迟间隔(ms) - 控制多条线依次出现

  // ==================== 加载动画配置 ====================
  baseLoadingDelay: 300, // 基础加载完成后的额外延迟(ms)
  loadingText: "数据加载中...", // 加载提示文本
  loadingColor: "#32FEFC", // 加载动画颜色 - 与主题色保持一致
  loadingTextColor: "#ffffff", // 加载文本颜色
  loadingMaskColor: "rgba(0, 0, 0, 0.3)", // 加载遮罩颜色
  loadingFontSize: 12, // 加载文本字体大小
  loadingSpinnerRadius: 10, // 加载旋转器半径
  loadingLineWidth: 2, // 加载线条宽度
};

// 计算属性：根据速度倍数计算实际的动画参数
const computedAnimationConfig = computed(() => {
  const speed = animationConfig.speedMultiplier;
  return {
    duration: Math.round(animationConfig.baseDuration / speed),
    delayInterval: Math.round(animationConfig.baseDelayInterval / speed),
    loadingDelay: Math.round(animationConfig.baseLoadingDelay / speed),
  };
});

// 颜色配置
const colorConfig = {
  高危: "#FD9391", // 红色
  中危: "#97C3FD", // 蓝色
  底危: "#32FEFC", // 绿色
};

// 计算属性：从 dataStore 获取动态数据
const chartData = computed(() => {
  const warningSeven = dataStore.data?.warningSeven;
  const warningSevenItem = dataStore.data?.warningSevenItem;

  if (!warningSeven || !warningSevenItem) {
    // 如果数据还未加载，返回默认值
    return {
      xAxisData: ["07-13", "07-14", "07-15", "07-16", "07-17", "07-18", "07-19"],
      highData: [0, 0, 0, 0, 0, 0, 0],
      middleData: [0, 0, 0, 0, 0, 0, 0],
      lowData: [0, 0, 0, 0, 0, 0, 0],
    };
  }

  return {
    xAxisData: warningSevenItem || [],
    highData: warningSeven.high || [],
    middleData: warningSeven.middle || [],
    lowData: warningSeven.low || [],
  };
});

const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);

    if (props.enableLoadingAnimation) {
      // 如果启用加载动画，先显示加载状态
      myChart.showLoading({
        text: animationConfig.loadingText,
        color: animationConfig.loadingColor,
        textColor: animationConfig.loadingTextColor,
        maskColor: animationConfig.loadingMaskColor,
        zlevel: 0,
        fontSize: animationConfig.loadingFontSize,
        showSpinner: true,
        spinnerRadius: animationConfig.loadingSpinnerRadius,
        lineWidth: animationConfig.loadingLineWidth,
      });

      // 根据animationDelay延迟显示图表
      const delay = parseFloat(props.animationDelay) * 1000 || 0;
      setTimeout(() => {
        myChart.hideLoading();
        updateChart();
        isChartReady.value = true;
      }, delay + computedAnimationConfig.value.loadingDelay);
    } else {
      updateChart();
      isChartReady.value = true;
    }

    // 响应式处理
    window.addEventListener("resize", () => {
      myChart && myChart.resize();
    });
  }
};

const updateChart = () => {
  if (!myChart) return;

  const option = {
    backgroundColor: "transparent",
    // 添加动画配置
    animation: props.enableLoadingAnimation,
    animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.duration : 0,
    animationEasing: animationConfig.easing,
    animationDelay: function (idx) {
      return idx * computedAnimationConfig.value.delayInterval;
    },
    legend: {
      data: [
        { name: "高危", itemStyle: { color: colorConfig.高危 } },
        { name: "中危", itemStyle: { color: colorConfig.中危 } },
        { name: "底危", itemStyle: { color: colorConfig.底危 } },
      ],
      right: "10%",
      top: "5%",
      textStyle: {
        color: "#ffffff",
        fontSize: 12,
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 15,
      //圆圈
      icon: "circle",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "8%",
      top: "25%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: chartData.value.xAxisData,
      axisLine: {
        lineStyle: {
          color: "#00d4ff",
        },
      },
      axisLabel: {
        color: "#a1a1a1",
        fontSize: 12,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#a1a1a1",
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.1)",
          type: "solid",
        },
      },
      // 设置Y轴刻度间隔，避免数字太挤
      min: 0, // 明确设置最小值
      max: 800, // 设置固定最大值，确保间隔均匀
      interval: 200, // 设置固定间隔为200，显示0, 200, 400, 600, 800
    },
    series: [
      {
        name: "高危",
        data: chartData.value.highData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.高危,
          width: 2,
        },
        // 添加系列级别的动画配置
        animation: props.enableLoadingAnimation,
        animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.duration : 0,
        animationEasing: animationConfig.easing,
        animationDelay: 0, // 第一条线立即开始
      },
      {
        name: "中危",
        data: chartData.value.middleData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.中危,
          width: 2,
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: colorConfig.中危 + "66", // 顶部颜色，40%透明度
              },
              {
                offset: 1,
                color: colorConfig.中危 + "0D", // 底部颜色，5%透明度
              },
            ],
          },
        },
        // 添加系列级别的动画配置
        animation: props.enableLoadingAnimation,
        animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.duration : 0,
        animationEasing: animationConfig.easing,
        animationDelay: computedAnimationConfig.value.delayInterval, // 第二条线延迟出现
      },
      {
        name: "底危",
        data: chartData.value.lowData,
        type: "line",
        smooth: true,
        symbol: "none", // 去除圆点
        lineStyle: {
          color: colorConfig.底危,
          width: 2,
        },
        // 添加系列级别的动画配置
        animation: props.enableLoadingAnimation,
        animationDuration: props.enableLoadingAnimation ? computedAnimationConfig.value.duration : 0,
        animationEasing: animationConfig.easing,
        animationDelay: computedAnimationConfig.value.delayInterval * 2, // 第三条线最后出现
      },
    ],
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#00d4ff",
      textStyle: {
        color: "#ffffff",
      },
    },
  };

  myChart.setOption(option);
};

// 监听数据变化，自动更新图表
watch(
  chartData,
  () => {
    // 只有在图表准备好后才更新
    if (isChartReady.value) {
      updateChart();
    }
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
  window.removeEventListener("resize", () => {
    myChart && myChart.resize();
  });
});
</script>

<style lang="less" scoped>
.Qx1 {
  width: 100%;
  height: 100%;
  pointer-events: all;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
